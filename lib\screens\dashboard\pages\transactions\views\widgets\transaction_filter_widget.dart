// Comprehensive Transaction Filter Widget
// Provides all filter options: search, phone, transaction code, date range

import 'dart:async';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:onekitty/helpers/colors.dart';
import 'package:onekitty/utils/themes_colors.dart';
import '../../models/transaction_type.dart';
import '../../controllers/transaction_controller.dart';

class TransactionFilterWidget extends StatefulWidget {
  final TransactionType transactionType;
  final String controllerTag;

  const TransactionFilterWidget({
    super.key,
    required this.transactionType,
    required this.controllerTag,
  });

  @override
  State<TransactionFilterWidget> createState() => _TransactionFilterWidgetState();
}

class _TransactionFilterWidgetState extends State<TransactionFilterWidget> {
  final TextEditingController _searchController = TextEditingController();
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _transactionCodeController = TextEditingController();
  
  DateTime? _startDate;
  DateTime? _endDate;
  bool _isExpanded = false;

  late final TransactionController _controller;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<TransactionController>(tag: widget.controllerTag);
    
    // Initialize controllers with current filter values
    _searchController.text = _controller.searchFilter;
    _phoneController.text = _controller.phoneFilter;
    _transactionCodeController.text = _controller.transactionCodeFilter;
  }

  @override
  void dispose() {
    _searchController.dispose();
    _phoneController.dispose();
    _transactionCodeController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(16.w),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12.r),
        border: Border.all(color: AppColors.slate),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search field - always visible
          _buildSearchField(),
          
          // Expandable filters section
          if (_isExpanded) ...[
            const Divider(color: AppColors.slate, height: 1),
            _buildAdvancedFilters(),
          ],
          
          // Expand/collapse button and clear filters
          _buildFilterActions(),
        ],
      ),
    );
  }

  Widget _buildSearchField() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: TextField(
        controller: _searchController,
        onChanged: (value) {
          // Debounce the search to avoid too many API calls
          _debounceSearch(value);
        },
        decoration: InputDecoration(
          hintText: _getSearchHint(),
          hintStyle: const TextStyle(
            color: Colors.black54,
            fontSize: 14,
          ),
          prefixIcon: const Icon(
            Icons.search,
            color: AppColors.neutralGrey,
          ),
          suffixIcon: _searchController.text.isNotEmpty
              ? IconButton(
                  icon: const Icon(
                    Icons.clear,
                    color: AppColors.neutralGrey,
                  ),
                  onPressed: () {
                    _searchController.clear();
                    _controller.setSearchFilter('');
                  },
                )
              : null,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: 16.w,
            vertical: 12.h,
          ),
        ),
        style: const TextStyle(
          color: Colors.black87,
          fontSize: 14,
        ),
      ),
    );
  }

  Widget _buildAdvancedFilters() {
    return Padding(
      padding: EdgeInsets.all(16.w),
      child: Column(
        children: [
          // Phone number filter
          _buildFilterField(
            controller: _phoneController,
            label: 'Phone Number',
            hint: 'Enter phone number',
            icon: Icons.phone,
            onChanged: (value) => _controller.setPhoneFilter(value),
          ),
          
          SizedBox(height: 12.h),
          
          // Transaction code filter
          _buildFilterField(
            controller: _transactionCodeController,
            label: 'Transaction Code',
            hint: 'Enter transaction code',
            icon: Icons.receipt,
            onChanged: (value) => _controller.setTransactionCodeFilter(value),
          ),
          
          SizedBox(height: 12.h),
          
          // Date range filter
          _buildDateRangeFilter(),
        ],
      ),
    );
  }

  Widget _buildFilterField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required Function(String) onChanged,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: const TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 4.h),
        TextField(
          controller: controller,
          onChanged: onChanged,
          decoration: InputDecoration(
            hintText: hint,
            hintStyle: const TextStyle(
              color: Colors.black54,
              fontSize: 14,
            ),
            prefixIcon: Icon(
              icon,
              color: AppColors.neutralGrey,
              size: 20,
            ),
            suffixIcon: controller.text.isNotEmpty
                ? IconButton(
                    icon: const Icon(
                      Icons.clear,
                      color: AppColors.neutralGrey,
                      size: 18,
                    ),
                    onPressed: () {
                      controller.clear();
                      onChanged('');
                    },
                  )
                : null,
            border: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.slate),
            ),
            enabledBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.slate),
            ),
            focusedBorder: OutlineInputBorder(
              borderRadius: BorderRadius.circular(8.r),
              borderSide: const BorderSide(color: AppColors.primary),
            ),
            contentPadding: EdgeInsets.symmetric(
              horizontal: 12.w,
              vertical: 10.h,
            ),
          ),
          style: const TextStyle(
            color: Colors.black87,
            fontSize: 14,
          ),
        ),
      ],
    );
  }

  Widget _buildDateRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Date Range',
          style: TextStyle(
            fontSize: 12,
            fontWeight: FontWeight.w500,
            color: Colors.black87,
          ),
        ),
        SizedBox(height: 4.h),
        Row(
          children: [
            Expanded(
              child: _buildDateField(
                label: 'Start Date',
                date: _startDate,
                onTap: () => _selectStartDate(),
              ),
            ),
            SizedBox(width: 12.w),
            Expanded(
              child: _buildDateField(
                label: 'End Date',
                date: _endDate,
                onTap: () => _selectEndDate(),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildDateField({
    required String label,
    required DateTime? date,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 10.h),
        decoration: BoxDecoration(
          border: Border.all(color: AppColors.slate),
          borderRadius: BorderRadius.circular(8.r),
        ),
        child: Row(
          children: [
            const Icon(
              Icons.calendar_today,
              color: AppColors.neutralGrey,
              size: 20,
            ),
            SizedBox(width: 8.w),
            Expanded(
              child: Text(
                date != null 
                    ? DateFormat('dd/MM/yyyy').format(date)
                    : label,
                style: TextStyle(
                  color: date != null ? Colors.black87 : Colors.black54,
                  fontSize: 14,
                ),
              ),
            ),
            if (date != null)
              GestureDetector(
                onTap: () {
                  setState(() {
                    if (label == 'Start Date') {
                      _startDate = null;
                    } else {
                      _endDate = null;
                    }
                  });
                  _updateDateFilters();
                },
                child: const Icon(
                  Icons.clear,
                  color: AppColors.neutralGrey,
                  size: 18,
                ),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildFilterActions() {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: 16.w, vertical: 8.h),
      child: Row(
        children: [
          // Expand/collapse button
          TextButton.icon(
            onPressed: () {
              setState(() {
                _isExpanded = !_isExpanded;
              });
            },
            icon: Icon(
              _isExpanded ? Icons.expand_less : Icons.expand_more,
              color: AppColors.primary,
            ),
            label: Text(
              _isExpanded ? 'Less Filters' : 'More Filters',
              style: TextStyle(
                color: AppColors.primary,
                fontSize: 12.sp,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          
          const Spacer(),
          
          // Clear all filters button
          if (_hasActiveFilters())
            TextButton.icon(
              onPressed: _clearAllFilters,
              icon: const Icon(
                Icons.clear_all,
                color: ColorUtil.error,
                size: 16,
              ),
              label: Text(
                'Clear All',
                style: TextStyle(
                  color: ColorUtil.error,
                  fontSize: 12.sp,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
        ],
      ),
    );
  }

  String _getSearchHint() {
    switch (widget.transactionType) {
      case TransactionType.user:
        return 'Search your transactions...';
      case TransactionType.kitty:
        return 'Search kitty transactions...';
      case TransactionType.chama:
        return 'Search chama transactions...';
      case TransactionType.event:
        return 'Search event transactions...';
    }
  }

  bool _hasActiveFilters() {
    return _searchController.text.isNotEmpty ||
           _phoneController.text.isNotEmpty ||
           _transactionCodeController.text.isNotEmpty ||
           _startDate != null ||
           _endDate != null;
  }

  void _clearAllFilters() {
    setState(() {
      _searchController.clear();
      _phoneController.clear();
      _transactionCodeController.clear();
      _startDate = null;
      _endDate = null;
    });
    _controller.clearAllFilters();
  }

  void _selectStartDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _startDate ?? DateTime.now(),
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _startDate = picked;
      });
      _updateDateFilters();
    }
  }

  void _selectEndDate() async {
    final DateTime? picked = await showDatePicker(
      context: context,
      initialDate: _endDate ?? DateTime.now(),
      firstDate: _startDate ?? DateTime(2020),
      lastDate: DateTime.now(),
    );
    if (picked != null) {
      setState(() {
        _endDate = picked;
      });
      _updateDateFilters();
    }
  }

  void _updateDateFilters() {
    final startDateStr = _startDate != null 
        ? DateFormat('yyyy-MM-dd').format(_startDate!)
        : null;
    final endDateStr = _endDate != null 
        ? DateFormat('yyyy-MM-dd').format(_endDate!)
        : null;
    
    _controller.setDateFilters(startDateStr, endDateStr);
  }

  // Debounce search to avoid too many API calls
  Timer? _debounceTimer;
  void _debounceSearch(String value) {
    _debounceTimer?.cancel();
    _debounceTimer = Timer(const Duration(milliseconds: 500), () {
      _controller.setSearchFilter(value);
    });
  }
}
